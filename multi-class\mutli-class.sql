
            CREATE OR REPLACE FUNCTION sandf.fn_get_plan_design_report_multi_class(
    plan_uuid_param TEXT,
    user_id_param TEXT DEFAULT NULL,
    includes_param TEXT[] DEFAULT NULL,
    excludes_param TEXT[] DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$

DECLARE
    plan_details_key TEXT := 'planDetails';
    quote_record RECORD;
    plan_details JSONB;
    fields JSONB;
    field_item JSONB;
    carriers_array JSONB := '[]'::jsonb;
    sections_array JSONB := '[]'::jsonb;
    section_obj JSONB;
    benefits_array JSONB;
    benefit_obj JSONB;
    carrier_name TEXT;
    group_name TEXT;
    group_display_name TEXT;
    benefit_name TEXT;
    benefit_key TEXT;
    benefit_values JSONB := '{}'::jsonb;
    field_details JSONB;
    field_detail JSONB;
    carrier_value TEXT;
    section_map JSONB := '{}'::jsonb;
    section_original_names JSONB := '{}'::jsonb;
    section_names TEXT[];
    section_name TEXT;
    section_id TEXT;
    section_display_name TEXT;
    benefit_map JSONB := '{}'::jsonb;
    benefit_keys TEXT[];
    benefit_key_item TEXT;
    section_order_record RECORD;
    benefit_order_record RECORD;
    skip_fields TEXT[] := ARRAY['maximumLife', 'maximumADAD'];
    coverage_life_values JSONB := '{}'::jsonb;
    field_values_to_check JSONB := '{}'::jsonb;
    should_skip_field BOOLEAN;
    carrier_check TEXT;
    coverage_value TEXT;
    field_value TEXT;
    carrier_order_map JSONB := '{}'::jsonb;
    ordered_carriers_array JSONB := '[]'::jsonb;
    carrier_item TEXT;
    carrier_order INTEGER;
    quote_uuid_val UUID;
    MAX_BENEFITS_PER_PAGE INTEGER := 10;
    all_benefits JSONB := '[]'::jsonb;
    all_sections JSONB := '[]'::jsonb;
    current_page_benefits INTEGER := 0;
    current_page_sections JSONB := '[]'::jsonb;
    current_section_benefits JSONB := '[]'::jsonb;
    result_pages JSONB := '[]'::jsonb;
    total_benefits INTEGER;
    current_section_name TEXT;
    current_section_id TEXT;
    current_section_display_name TEXT;
    section_idx INTEGER;
    benefit_idx INTEGER;
    employee_classes TEXT[];
    employee_class_count INTEGER;
    current_employee_class TEXT;
    class_suffix TEXT;
    class_index INTEGER;
    class_suffix_extracted TEXT;
    page_object JSONB;
BEGIN
    SELECT COUNT(DISTINCT ec.name), array_agg(DISTINCT ec.name ORDER BY ec.name)
    INTO employee_class_count, employee_classes
    FROM sandf.plan p
    JOIN sandf.quote q ON q.plan_id = p.plan_id
    JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
    JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
    WHERE p.plan_uuid = plan_uuid_param::uuid
    AND ecq.formatted_quote_details IS NOT NULL
    AND ecq.formatted_quote_details != '{}'::jsonb;
    FOR quote_record IN
        SELECT ecq.formatted_quote_details,
               c.description as carrier_description,
               q.quote_id,
               q.quote_uuid,
               ec.name as employee_class_name
        FROM sandf.plan p
        JOIN sandf.quote q ON q.plan_id = p.plan_id
        JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
        JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
        JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
        WHERE p.plan_uuid = plan_uuid_param::uuid
        AND ec.name = ANY(employee_classes)
        AND ecq.formatted_quote_details IS NOT NULL
        AND ecq.formatted_quote_details != '{}'::jsonb
    LOOP
        carrier_name := quote_record.carrier_description;

        carrier_order := sandf.get_user_preference_order(
            user_id_param,
            plan_uuid_param,
            quote_record.quote_id,
            quote_record.quote_uuid,
            999999
        );

        IF NOT carrier_order_map ? carrier_name THEN
            carrier_order_map := carrier_order_map || jsonb_build_object(
                carrier_name,
                jsonb_build_object(
                    'order', carrier_order,
                    'quote_id', quote_record.quote_id,
                    'quote_uuid', quote_record.quote_uuid
                )
            );
        END IF;
    END LOOP;

    class_index := 1;
    FOREACH current_employee_class IN ARRAY employee_classes
    LOOP
        class_suffix := current_employee_class;
        FOR quote_record IN
            SELECT ecq.formatted_quote_details,
                   c.description as carrier_description,
                   q.quote_id,
                   q.quote_uuid
            FROM sandf.plan p
            JOIN sandf.quote q ON q.plan_id = p.plan_id
            JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
            JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
            JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
            WHERE p.plan_uuid = plan_uuid_param::uuid
            AND ec.name = current_employee_class
            AND ecq.formatted_quote_details IS NOT NULL
            AND ecq.formatted_quote_details != '{}'::jsonb
            ORDER BY
                COALESCE((carrier_order_map -> c.description ->> 'order')::integer, 999999) ASC,
                c.description ASC
        LOOP
            carrier_name := quote_record.carrier_description;
            plan_details := quote_record.formatted_quote_details -> plan_details_key;

            IF plan_details IS NOT NULL AND jsonb_typeof(plan_details) = 'object' THEN
                fields := plan_details -> 'fields';
                IF fields IS NOT NULL AND jsonb_typeof(fields) = 'array' THEN
                    FOR field_item IN SELECT jsonb_array_elements(fields)
                    LOOP
                        group_name := field_item ->> 'groupName';
                        IF group_name IS NULL THEN
                            group_name := field_item ->> 'name';
                        END IF;

                        SELECT friendly INTO group_display_name
                        FROM sandf.ui_field
                        WHERE name = group_name
                        LIMIT 1;

                        IF group_display_name IS NULL THEN
                            group_display_name := COALESCE(field_item ->> 'friendly', group_name);
                        END IF;

                        IF (includes_param IS NULL OR group_name = ANY(includes_param))
                        AND (excludes_param IS NULL OR NOT (group_name = ANY(excludes_param))) THEN

                            -- For multi-class, append class suffix to section mapping
                            section_map := section_map || jsonb_build_object(
                                group_name || class_suffix,
                                group_display_name || ' - ' || current_employee_class
                            );

                            -- Store original section name for ui_field lookup
                            section_original_names := section_original_names || jsonb_build_object(
                                group_name || class_suffix,
                                group_name
                            );

                            field_details := field_item -> 'fields';
                            IF field_details IS NOT NULL AND jsonb_typeof(field_details) = 'array' THEN
                                FOR field_detail IN SELECT jsonb_array_elements(field_details)
                                LOOP
                                    benefit_name := field_detail ->> 'friendly';
                                    benefit_key := field_detail ->> 'name';
                                    carrier_value := field_detail ->> 'value';

                                    IF carrier_value IS NULL OR trim(carrier_value) = '' THEN
                                        carrier_value := '-';
                                    ELSE
                                        BEGIN
                                            PERFORM sandf.safe_parse_numeric(carrier_value);
                                        EXCEPTION WHEN OTHERS THEN
                                            carrier_value := '-';
                                        END;
                                    END IF;

                                    IF benefit_key = 'coverageLife' THEN
                                        coverage_life_values := coverage_life_values || jsonb_build_object(
                                            carrier_name || '_' || class_suffix,
                                            carrier_value
                                        );
                                    END IF;

                                    IF benefit_key = ANY(skip_fields) THEN
                                        IF NOT field_values_to_check ? (benefit_key || '_' || class_suffix) THEN
                                            field_values_to_check := field_values_to_check || jsonb_build_object(
                                                benefit_key || '_' || class_suffix,
                                                '{}'::jsonb
                                            );
                                        END IF;
                                        field_values_to_check := jsonb_set(
                                            field_values_to_check,
                                            ARRAY[benefit_key || '_' || class_suffix, carrier_name],
                                            to_jsonb(carrier_value)
                                        );
                                    END IF;

                                    benefit_key_item := group_name || class_suffix || '.' || benefit_key;

                                    IF benefit_name IS NULL THEN
                                        SELECT friendly INTO benefit_name
                                        FROM sandf.ui_field
                                        WHERE name = benefit_key
                                        LIMIT 1;
                                        IF benefit_name IS NULL THEN
                                            benefit_name := benefit_key;
                                        END IF;
                                    END IF;

                                    IF NOT benefit_map ? benefit_key_item THEN
                                        benefit_map := benefit_map || jsonb_build_object(
                                            benefit_key_item,
                                            jsonb_build_object(
                                                'name', benefit_name,
                                                'key', benefit_key,
                                                'section', lower(replace(replace(replace(group_name, ' ', ''), '&', ''), '''', '')) || '-' || lower(replace(replace(trim(class_suffix), ' ', ''), '''', '')),
                                                'values', '{}'::jsonb
                                            )
                                        );
                                    END IF;

                                    benefit_map := jsonb_set(
                                        benefit_map,
                                        ARRAY[benefit_key_item, 'values', carrier_name],
                                        to_jsonb(carrier_value)
                                    );
                                END LOOP;
                            END IF;
                        END IF;
                    END LOOP;
                ELSE
                    DECLARE
                        benefit_data JSONB;
                        field_key TEXT;
                        show_value TEXT;
                    BEGIN
                        FOR benefit_key IN SELECT jsonb_object_keys(plan_details)
                        LOOP
                            IF (includes_param IS NULL OR benefit_key = ANY(includes_param))
                            AND (excludes_param IS NULL OR NOT (benefit_key = ANY(excludes_param))) THEN

                                benefit_data := plan_details -> benefit_key;

                                IF benefit_data IS NULL OR jsonb_typeof(benefit_data) != 'object' THEN
                                    CONTINUE;
                                END IF;

                                show_value := benefit_data ->> 'show';
                                IF show_value IS NOT NULL AND show_value::boolean = false THEN
                                    CONTINUE;
                                END IF;

                                SELECT friendly INTO section_display_name
                                FROM sandf.ui_field
                                WHERE name = benefit_key
                                LIMIT 1;

                                IF section_display_name IS NULL THEN
                                    section_display_name := benefit_key;
                                END IF;

                                section_map := section_map || jsonb_build_object(
                                    benefit_key || class_suffix,
                                    section_display_name || ' - ' || current_employee_class
                                );

                                section_original_names := section_original_names || jsonb_build_object(
                                    benefit_key || class_suffix,
                                    benefit_key
                                );

                                FOR field_key IN SELECT jsonb_object_keys(benefit_data)
                                LOOP
                                    IF field_key = 'show' THEN
                                        CONTINUE;
                                    END IF;

                                    carrier_value := benefit_data ->> field_key;
                                    IF carrier_value IS NULL OR trim(carrier_value) = '' THEN
                                        carrier_value := '-';
                                    ELSE
                                        BEGIN
                                            PERFORM sandf.safe_parse_numeric(carrier_value);
                                        EXCEPTION WHEN OTHERS THEN
                                            carrier_value := '-';
                                        END;
                                    END IF;

                                    IF field_key = 'coverageLife' THEN
                                        coverage_life_values := coverage_life_values || jsonb_build_object(
                                            carrier_name || '_' || class_suffix,
                                            carrier_value
                                        );
                                    END IF;

                                    IF field_key = ANY(skip_fields) THEN
                                        IF NOT field_values_to_check ? (field_key || '_' || class_suffix) THEN
                                            field_values_to_check := field_values_to_check || jsonb_build_object(
                                                field_key || '_' || class_suffix,
                                                '{}'::jsonb
                                            );
                                        END IF;
                                        field_values_to_check := jsonb_set(
                                            field_values_to_check,
                                            ARRAY[field_key || '_' || class_suffix, carrier_name],
                                            to_jsonb(carrier_value)
                                        );
                                    END IF;

                                    benefit_key_item := benefit_key || class_suffix || '.' || field_key;

                                    SELECT friendly INTO benefit_name
                                    FROM sandf.ui_field
                                    WHERE name = field_key
                                    LIMIT 1;

                                    IF benefit_name IS NULL THEN
                                        benefit_name := field_key;
                                    END IF;

                                    IF NOT benefit_map ? benefit_key_item THEN
                                        benefit_map := benefit_map || jsonb_build_object(
                                            benefit_key_item,
                                            jsonb_build_object(
                                                'name', benefit_name,
                                                'key', field_key,
                                                'section', lower(replace(replace(replace(benefit_key, ' ', ''), '&', ''), '''', '')) || '-' || lower(replace(replace(trim(class_suffix), ' ', ''), '''', '')),
                                                'values', '{}'::jsonb
                                            )
                                        );
                                    END IF;

                                    benefit_map := jsonb_set(
                                        benefit_map,
                                        ARRAY[benefit_key_item, 'values', carrier_name],
                                        to_jsonb(carrier_value)
                                    );
                                END LOOP;
                            END IF;
                        END LOOP;
                    END;
                END IF;
            END IF;
        END LOOP;

        class_index := class_index + 1;
    END LOOP;

    -- Normalize benefit_map to ensure all benefits have values for all carriers
    DECLARE
        all_carriers TEXT[];
        benefit_key_norm TEXT;
        carrier_name_norm TEXT;
        benefit_values_norm JSONB;
    BEGIN
        -- Get all unique carrier names
        SELECT array_agg(DISTINCT key ORDER BY key) INTO all_carriers
        FROM jsonb_each(carrier_order_map);

        -- For each benefit, ensure all carriers are present
        FOR benefit_key_norm IN SELECT jsonb_object_keys(benefit_map)
        LOOP
            benefit_values_norm := benefit_map -> benefit_key_norm -> 'values';

            -- Add missing carriers with "-" value
            FOREACH carrier_name_norm IN ARRAY all_carriers
            LOOP
                IF NOT (benefit_values_norm ? carrier_name_norm) THEN
                    benefit_map := jsonb_set(
                        benefit_map,
                        ARRAY[benefit_key_norm, 'values', carrier_name_norm],
                        to_jsonb('-'::text)
                    );
                END IF;
            END LOOP;
        END LOOP;
    END;

    -- Step 4.5: Group benefits by matching values across classes and create final sections array
    DECLARE
        class_sections_map JSONB := '{}'::jsonb;  -- Store sections by class
        combined_sections_map JSONB := '{}'::jsonb;  -- Store final combined sections
        section_signature TEXT;  -- For matching benefit values across classes
        matching_classes TEXT[];  -- Classes with matching benefit values
        combined_class_name TEXT;  -- Final className for section
        class_to_letter_map JSONB := '{}'::jsonb;
        letter_index INTEGER := 1;
        current_letter TEXT;
        all_section_keys TEXT[];
        section_key_item TEXT;
        class_section JSONB;
        signature_to_classes JSONB := '{}'::jsonb;
        signature_to_section JSONB := '{}'::jsonb;
        processed_signatures TEXT[] := ARRAY[]::TEXT[];
        base_section_key TEXT;
        base_benefit_key TEXT;
        -- Variables for final section building
        temp_sections_array JSONB := '[]'::jsonb;
        ordered_section RECORD;
        section_benefits_map JSONB := '{}'::jsonb;
        current_section_id TEXT;
        current_section_display_name TEXT;
        benefits_for_section JSONB := '[]'::jsonb;
        -- Variables for benefit processing
        combined_benefit_keys TEXT[];
        benefit_key_norm_local TEXT;
        benefit_obj_local JSONB;
        current_section_id_local TEXT;
        current_section_display_name_local TEXT;
        benefits_for_section_local JSONB;
        -- Variables for benefit reorganization
        benefit_keys_array TEXT[];
        base_benefit_key_local TEXT;
        base_section_key_local TEXT;
        section_key_item_local TEXT;
    BEGIN
        -- Build class to letter mapping (A, B, C, etc.)
        FOREACH current_employee_class IN ARRAY employee_classes
        LOOP
            current_letter := chr(64 + letter_index); -- A=65, B=66, etc.
            class_to_letter_map := class_to_letter_map || jsonb_build_object(
                current_employee_class, current_letter
            );
            letter_index := letter_index + 1;
        END LOOP;

        -- First, reorganize benefit_map by class and base benefit key
        SELECT array_agg(key_name) INTO benefit_keys_array
        FROM (SELECT jsonb_object_keys(benefit_map) as key_name) subq;

        -- Check if we have any benefit keys to process
        IF benefit_keys_array IS NULL OR array_length(benefit_keys_array, 1) IS NULL THEN
            benefit_keys_array := ARRAY[]::TEXT[];
        END IF;

        FOREACH benefit_key_norm_local IN ARRAY benefit_keys_array
        LOOP
            -- Extract class suffix and base benefit key
            -- benefit_key_norm_local format: "groupName + classSuffix + '.' + benefitKey"
            FOR current_employee_class IN SELECT unnest(employee_classes)
            LOOP
                IF benefit_key_norm_local LIKE '%' || current_employee_class || '.%' THEN
                    base_benefit_key_local := split_part(benefit_key_norm_local, '.', 2);
                    base_section_key_local := replace(benefit_key_norm_local, '.' || base_benefit_key_local, '');
                    base_section_key_local := replace(base_section_key_local, current_employee_class, '');

                    section_key_item_local := base_section_key_local || '.' || base_benefit_key_local;

                    -- Store in class sections map
                    class_sections_map := jsonb_set(
                        class_sections_map,
                        ARRAY[current_employee_class, section_key_item_local],
                        benefit_map -> benefit_key_norm_local
                    );
                    EXIT;
                END IF;
            END LOOP;
        END LOOP;

        -- Get all unique section keys across all classes
        SELECT array_agg(DISTINCT subq.section_key)
        INTO all_section_keys
        FROM (
            SELECT jsonb_object_keys(class_data.class_sections) as section_key
            FROM jsonb_each(class_sections_map) as class_data(class_name, class_sections)
        ) subq;

        -- Check if we have any section keys to process
        IF all_section_keys IS NULL OR array_length(all_section_keys, 1) IS NULL THEN
            all_section_keys := ARRAY[]::TEXT[];
        END IF;

        -- For each section key, group classes with matching benefit values
        FOREACH section_key_item IN ARRAY all_section_keys
        LOOP
            signature_to_classes := '{}'::jsonb;
            signature_to_section := '{}'::jsonb;

            -- Check each class for this section key
            FOR current_employee_class IN
                SELECT jsonb_object_keys(class_sections_map)
            LOOP
                class_section := class_sections_map -> current_employee_class -> section_key_item;

                IF class_section IS NOT NULL THEN
                    -- Create signature from benefit values for matching
                    section_signature := '';
                    FOR carrier_name IN
                        SELECT jsonb_object_keys(class_section -> 'values')
                    LOOP
                        section_signature := section_signature || carrier_name || ':' ||
                            COALESCE(class_section -> 'values' ->> carrier_name, '') || ';';
                    END LOOP;

                    -- Group classes by signature
                    IF signature_to_classes ? section_signature THEN
                        signature_to_classes := jsonb_set(
                            signature_to_classes,
                            ARRAY[section_signature],
                            (signature_to_classes -> section_signature) || jsonb_build_array(current_employee_class)
                        );
                    ELSE
                        signature_to_classes := signature_to_classes || jsonb_build_object(
                            section_signature,
                            jsonb_build_array(current_employee_class)
                        );
                        signature_to_section := signature_to_section || jsonb_build_object(
                            section_signature,
                            class_section
                        );
                    END IF;
                END IF;
            END LOOP;

            -- Create combined sections for each signature
            FOR section_signature IN
                SELECT jsonb_object_keys(signature_to_classes)
            LOOP
                matching_classes := ARRAY(SELECT jsonb_array_elements_text(signature_to_classes -> section_signature));
                section_obj := signature_to_section -> section_signature;

                -- Build className based on matching classes
                IF array_length(matching_classes, 1) = employee_class_count THEN
                    combined_class_name := 'ALL';
                ELSE
                    -- Convert class names to letters and join
                    DECLARE
                        class_letters TEXT[] := ARRAY[]::TEXT[];
                        class_item TEXT;
                    BEGIN
                        FOREACH class_item IN ARRAY matching_classes
                        LOOP
                            class_letters := array_append(class_letters, class_to_letter_map ->> class_item);
                        END LOOP;
                        combined_class_name := array_to_string(class_letters, ',');
                    END;
                END IF;

                -- Update section with combined class name
                base_section_key := split_part(section_key_item, '.', 1);
                section_id := lower(replace(replace(replace(base_section_key, ' ', ''), '&', ''), '''', '')) || combined_class_name;
                section_display_name := COALESCE(section_map ->> (base_section_key || employee_classes[1]), base_section_key);
                section_display_name := regexp_replace(section_display_name, ' - .*$', '') || ' ' || combined_class_name;

                section_obj := jsonb_set(section_obj, ARRAY['section'], to_jsonb(section_id));

                -- Store in combined sections map
                combined_sections_map := combined_sections_map || jsonb_build_object(
                    section_id || '.' || split_part(section_key_item, '.', 2),
                    jsonb_build_object(
                        'name', section_obj ->> 'name',
                        'key', section_obj ->> 'key',
                        'section', section_id,
                        'section_display_name', section_display_name,
                        'values', section_obj -> 'values',
                        'sort_key', base_section_key,
                        'combined_class_name', combined_class_name
                    )
                );
            END LOOP;
        END LOOP;

        FOR carrier_item IN
            SELECT key as carrier_name
            FROM jsonb_each(carrier_order_map)
            ORDER BY (value ->> 'order')::integer ASC, key ASC
        LOOP
            ordered_carriers_array := ordered_carriers_array || jsonb_build_array(carrier_item);
        END LOOP;

        -- Group benefits by section
        SELECT array_agg(key_name) INTO combined_benefit_keys
        FROM (SELECT jsonb_object_keys(combined_sections_map) as key_name) subq;

        -- Check if we have any benefit keys to process
        IF combined_benefit_keys IS NULL OR array_length(combined_benefit_keys, 1) IS NULL THEN
            combined_benefit_keys := ARRAY[]::TEXT[];
        END IF;

        -- Process each benefit key
        FOREACH benefit_key_norm_local IN ARRAY combined_benefit_keys
        LOOP
            benefit_obj_local := combined_sections_map -> benefit_key_norm_local;
            current_section_id_local := benefit_obj_local ->> 'section';
            current_section_display_name_local := benefit_obj_local ->> 'section_display_name';

            IF NOT section_benefits_map ? current_section_id_local THEN
                section_benefits_map := section_benefits_map || jsonb_build_object(
                    current_section_id_local,
                    jsonb_build_object(
                        'name', current_section_display_name_local,
                        'id', current_section_id_local,
                        'benefits', '[]'::jsonb,
                        'sort_key', benefit_obj_local ->> 'sort_key',
                        'combined_class_name', benefit_obj_local ->> 'combined_class_name'
                    )
                );
            END IF;

            -- Add benefit to section
            benefits_for_section_local := section_benefits_map -> current_section_id_local -> 'benefits';
            benefits_for_section_local := benefits_for_section_local || jsonb_build_array(
                jsonb_build_object(
                    'name', benefit_obj_local ->> 'name',
                    'key', benefit_obj_local ->> 'key',
                    'values', benefit_obj_local -> 'values',
                    'section', current_section_id_local
                )
            );

            section_benefits_map := jsonb_set(
                section_benefits_map,
                ARRAY[current_section_id_local, 'benefits'],
                benefits_for_section_local
            );
        END LOOP;

        -- Order sections by display_order from ui_field table, then by class letter order
        FOR ordered_section IN
            SELECT
                section_data as section_data,
                COALESCE(uf.display_order, 999999) as sort_order,
                section_data->>'sort_key' as section_key,
                section_data->>'combined_class_name' as class_name
            FROM jsonb_each(section_benefits_map) elem(section_id, section_data)
            LEFT JOIN sandf.ui_field uf ON uf.name = section_data->>'sort_key'
            ORDER BY
                sort_order ASC,
                section_key ASC,
                -- Order by class name: ALL first, then A, B, C, etc., then A,B combinations
                CASE
                    WHEN section_data->>'combined_class_name' = 'ALL' THEN 0
                    WHEN section_data->>'combined_class_name' ~ '^[A-Z]$' THEN ascii(section_data->>'combined_class_name') - 64  -- A=1, B=2, etc.
                    ELSE 1000 + length(section_data->>'combined_class_name')  -- A,B combinations come last
                END ASC,
                section_data->>'combined_class_name' ASC
        LOOP
            -- Remove metadata fields (keep only id, name, benefits)
            section_obj := ordered_section.section_data - 'sort_key' - 'combined_class_name';
            all_sections := all_sections || jsonb_build_array(section_obj);
        END LOOP;
    END;

    total_benefits := 0;
    FOR section_idx IN 0..jsonb_array_length(all_sections)-1 LOOP
        total_benefits := total_benefits + jsonb_array_length(all_sections -> section_idx -> 'benefits');
    END LOOP;

    IF total_benefits <= MAX_BENEFITS_PER_PAGE THEN
        RETURN jsonb_build_array(
            jsonb_build_object(
                'carriers', ordered_carriers_array,
                'sections', all_sections
            )
        );
    END IF;
    current_page_benefits := 0;
    current_page_sections := '[]'::jsonb;
    current_section_benefits := '[]'::jsonb;
    current_section_name := '';



    FOR section_idx IN 0..jsonb_array_length(all_sections)-1 LOOP
        section_obj := all_sections -> section_idx;
        section_name := section_obj ->> 'name';
        section_id := section_obj ->> 'id';
        benefits_array := section_obj -> 'benefits';

        FOR benefit_idx IN 0..jsonb_array_length(benefits_array)-1 LOOP
            benefit_obj := benefits_array -> benefit_idx;

            IF current_section_name != section_name THEN
                IF current_section_name != '' AND jsonb_array_length(current_section_benefits) > 0 THEN
                    current_page_sections := current_page_sections || jsonb_build_array(
                        jsonb_build_object(
                            'name', current_section_display_name,
                            'id', current_section_id,
                            'benefits', current_section_benefits
                        )
                    );
                END IF;

                current_section_name := section_name;
                current_section_id := section_id;
                current_section_display_name := section_name;
                current_section_benefits := '[]'::jsonb;
            END IF;

            IF current_page_benefits >= MAX_BENEFITS_PER_PAGE THEN
                IF jsonb_array_length(current_section_benefits) > 0 THEN
                    current_page_sections := current_page_sections || jsonb_build_array(
                        jsonb_build_object(
                            'name', current_section_display_name,
                            'id', current_section_id,
                            'benefits', current_section_benefits
                        )
                    );
                END IF;

                -- Build page object - include carriers on every page
                page_object := jsonb_build_object(
                    'carriers', ordered_carriers_array,
                    'sections', current_page_sections
                );

                result_pages := result_pages || jsonb_build_array(page_object);

                current_page_sections := '[]'::jsonb;
                current_section_benefits := '[]'::jsonb;
                current_page_benefits := 0;
            END IF;

            current_section_benefits := current_section_benefits || jsonb_build_array(benefit_obj);
            current_page_benefits := current_page_benefits + 1;
        END LOOP;
    END LOOP;

    IF current_section_name != '' AND jsonb_array_length(current_section_benefits) > 0 THEN
        current_page_sections := current_page_sections || jsonb_build_array(
            jsonb_build_object(
                'name', current_section_display_name,
                'id', current_section_id,
                'benefits', current_section_benefits
            )
        ); 
    END IF;

    IF jsonb_array_length(current_page_sections) > 0 THEN
        -- Build final page object - include carriers on every page
        page_object := jsonb_build_object(
            'carriers', ordered_carriers_array,
            'sections', current_page_sections
        );

        result_pages := result_pages || jsonb_build_array(page_object);
    END IF;

    RETURN result_pages;

END;
$$;
